=== Case: ar_small ===
Payload: {
  "loan": {
    "amount": "9000",
    "amountToCustomer": "9000",
    "country": "us",
    "loanInterestType": "pni",
    "province": "ar",
    "type": "business"
  },
  "query": "fee"
}
Response: [
  {
    "constraint": "GTE",
    "value": "0"
  },
  {
    "constraint": "LTE",
    "value": "100000000"
  }
]

=== Case: ar_large ===
Payload: {
  "loan": {
    "amount": "30000",
    "amountToCustomer": "30000",
    "country": "us",
    "loanInterestType": "pni",
    "province": "ar",
    "type": "business"
  },
  "query": "fee"
}
Response: [
  {
    "constraint": "GTE",
    "value": "0"
  },
  {
    "constraint": "LTE",
    "value": "100000000"
  }
]

