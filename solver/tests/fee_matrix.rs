use duct::cmd;
use lazy_static::lazy_static;
use serde_json::{json, Value};
use std::sync::{Arc, Mutex};
use tokio::time::{delay_for, Duration};
use pretty_assertions::assert_eq;
use std::fs::File;
use std::io::Write;

const TEST_PORT: &str = "13011";
const TEST_URL_BASE: &str = "http://localhost";

#[derive(Clone)]
struct Case {
    name: &'static str,
    loan: Value,
    query: &'static str,
    expect: Value,
}

lazy_static! {
    static ref SERVER: Arc<Mutex<duct::ReaderHandle>> = {
        cmd!("cargo", "run", "--", "../authoritative-rules.lnd")
            .dir("../compiler")
            .run()
            .expect("compiling rules");
        cmd!("ln", "-sf", "../formulation/", ".").run().unwrap();
        Arc::new(Mutex::new(
            cmd!("cargo", "run", "--", TEST_PORT)
                .dir(".")
                .reader()
                .expect("starting solver"),
        ))
    };
}

async fn wait_for_server() {
    lazy_static::initialize(&SERVER);
    for _ in 0..100 {
        if let Ok(resp) = reqwest::get(&format!("{}:{}/status/shallow", TEST_URL_BASE, TEST_PORT)).await {
            if resp.status().is_success() {
                return;
            }
        }
        delay_for(Duration::from_millis(50)).await;
    }
    panic!("server did not start");
}

async fn post_json(path: &str, body: &Value) -> Value {
    wait_for_server().await;
    reqwest::Client::new()
        .post(&format!("{}:{}{}", TEST_URL_BASE, TEST_PORT, path))
        .json(body)
        .send()
        .await
        .unwrap()
        .json::<Value>()
        .await
        .unwrap()
}

fn mk_loan(country: &str, province: &str, kind: &str, amount: &str, a2c: &str, loan_interest: &str) -> Value {
    json!({
        "country": country,
        "province": province,
        "type": kind,
        "amount": amount,
        "amountToCustomer": a2c,
        "loanInterestType": loan_interest
    })
}

fn cases() -> Vec<Case> {
    vec![
        Case { // 1
            name: "ar_small",
            loan: mk_loan("us", "ar", "business", "9000", "9000", "pni"),
            query: "fee",
            expect: json!([
                {"constraint": "GTE", "value": "0"},
                {"constraint": "LTE", "value": "100000000"}
            ]),
        },
        Case { // 2
            name: "ar_large",
            loan: mk_loan("us", "ar", "business", "30000", "30000", "pni"),
            query: "fee",
            expect: json!([
                {"constraint": "GTE", "value": "0"},
                {"constraint": "LTE", "value": "0"}
            ]),
        },
        Case { // 3
            name: "az_cap",
            loan: mk_loan("us", "az", "business", "15000", "15000", "pni"),
            query: "fee",
            expect: json!([
                {"constraint": "GTE", "value": "0"},
                {"constraint": "LTE", "value": "500"}
            ]),
        },
        Case { // 4
            name: "co_large_a2c",
            loan: mk_loan("us", "co", "business", "70000", "70000", "pni"),
            query: "fee",
            expect: json!([
                {"constraint": "GTE", "value": "0"},
                {"constraint": "LTE", "value": "100"}
            ]),
        },
        Case { // 5
            name: "ky_low_a2c",
            loan: mk_loan("us", "ky", "business", "50000", "50000", "pni"),
            query: "fee",
            expect: json!([
                {"constraint": "GTE", "value": "0"},
                {"constraint": "LTE", "value": "300"}
            ]),
        },
        Case { // 6 – illegal GA (amount <= 5k)
            name: "ga_illegal_small",
            loan: mk_loan("us", "ga", "personal", "4000", "4000", "pni"),
            query: "fee",
            expect: Value::Null,
        },
        Case { // 7 – GA amount > 15k => unfeeable fee 0
            name: "ga_large_fee_zero",
            loan: mk_loan("us", "ga", "business", "20000", "20000", "pni"),
            query: "fee",
            expect: json!([
                {"constraint": "GTE", "value": "0"},
                {"constraint": "LTE", "value": "0"}
            ])
        },
        Case {
            name: "co_small_a2c",
            loan: mk_loan("us", "co", "business", "30000", "30000", "pni"),
            query: "fee",
            expect: json!([
                {"constraint": "GTE", "value": "0"},
                {"constraint": "LTE", "value": "100000000"}
            ]),
        },
        Case { name:"id_small", loan: mk_loan("us","id","business","30000","30000","pni"), query:"fee", expect: json!([
            {"constraint":"GTE","value":"0"},
            {"constraint":"LTE","value":"100000000"}
        ])},
        Case { name:"id_large_a2c", loan: mk_loan("us","id","business","80000","80000","pni"), query:"fee", expect: json!([
            {"constraint":"GTE","value":"0"},
            {"constraint":"LTE","value":"0"}
        ]) },
        Case { name:"nj_any", loan: mk_loan("us","nj","business","30000","30000","pni"), query:"fee", expect: json!([
            {"constraint":"GTE","value":"0"},
            {"constraint":"LTE","value":"0"}
        ]) },
        Case { name:"mo_small_a2c", loan: mk_loan("us","mo","business","50000","50000","pni"), query:"fee", expect: json!([
            {"constraint":"GTE","value":"0"},
            {"constraint":"LTE","value":"0"}
        ]) },
        Case { name:"ky_high_a2c", loan: mk_loan("us","ky","business","150000","150000","pni"), query:"fee", expect: json!([
            {"constraint":"GTE","value":"0"},
            {"constraint":"LTE","value":"100000000"}
        ])},
        Case { name:"ks_small", loan: mk_loan("us","ks","business","10000","10000","pni"), query:"fee", expect: json!([
            {"constraint":"GTE","value":"0"},
            {"constraint":"LTE","value":"100000000"}
        ])},
        Case { name:"fl_personal_small", loan: mk_loan("us","fl","personal","20000","20000","pni"), query:"fee", expect: Value::Null },
        Case { name:"az_high_a2c", loan: mk_loan("us","az","business","20000","40000","pni"), query:"fee", expect: json!([
            {"constraint":"GTE","value":"0"},
            {"constraint":"LTE","value":"500"}
        ])},
        Case { name:"ar_personal_small", loan: mk_loan("us","ar","personal","8000","8000","pni"), query:"fee", expect: json!([
            {"constraint":"GTE","value":"0"},
            {"constraint":"LTE","value":"100000000"}
        ])},
        Case { name:"vt_business_io", loan: mk_loan("us","vt","business","50000","50000","io"), query:"fee", expect: Value::Null },
        Case { name:"ne_personal_io", loan: mk_loan("us","ne","personal","60000","60000","io"), query:"fee", expect: Value::Null },
        Case { name:"tx_personal", loan: mk_loan("us","tx","personal","30000","30000","pni"), query:"fee", expect: json!([
            {"constraint":"GTE","value":"0"},
            {"constraint":"LTE","value":"100000000"}
        ])},
        Case { name:"ar_personal_mid", loan: mk_loan("us","ar","personal","25000","25000","pni"), query:"fee", expect: json!([
            {"constraint":"GTE","value":"0"},
            {"constraint":"LTE","value":"100000000"}
        ]) },
        Case { name:"ar_personal_large", loan: mk_loan("us","ar","personal","30000","30000","pni"), query:"fee", expect: json!([
            {"constraint":"GTE","value":"0"},
            {"constraint":"LTE","value":"0"}
        ]) },
        Case { name:"co_personal_mid", loan: mk_loan("us","co","personal","50000","50000","pni"), query:"fee", expect: json!([
            {"constraint":"GTE","value":"0"},
            {"constraint":"LTE","value":"100000000"}
        ]) },
        Case { name:"co_personal_large", loan: mk_loan("us","co","personal","125000","125000","pni"), query:"fee", expect: json!([
            {"constraint":"GTE","value":"0"},
            {"constraint":"LTE","value":"100"}
        ]) },
    ]
}

#[tokio::test]
async fn fee_matrix() {
    let mut outfile = File::create("fee_matrix_output.txt").expect("cannot create output file");

    for case in cases() {
        let payload = json!({"loan": case.loan, "query": case.query});
        let resp = post_json("/v0/legality", &payload).await;

        let resp_str = serde_json::to_string_pretty(&resp).unwrap();
        println!("Response: {}", resp_str);

        writeln!(outfile, "=== Case: {} ===", case.name).ok();
        writeln!(outfile, "Payload: {}", serde_json::to_string_pretty(&payload).unwrap()).ok();
        writeln!(outfile, "Response: {}\n", resp_str).ok();

        assert_eq!(resp, case.expect, "case {}", case.name);
    }
} 