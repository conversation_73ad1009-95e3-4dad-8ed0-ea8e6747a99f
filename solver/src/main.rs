pub mod alpha2;
pub mod loan_instance;
pub mod num;
mod salt; // New namespace for SALT library code
pub mod solve_type;
pub mod solver;
mod warp_rejection;

use crate::solver::Solver;
use crate::warp_rejection::AnyhowError;
use alpha2::Alpha2;
use anyhow::{bail, Error};
use fraction::Fraction as BigFrac;
use fraction::GenericFraction;
use lazy_static::lazy_static;
use loan_instance::LoanInstance;
use lru_cache::LruCache;
use rweb::*;
use serde::{Deserialize, Serialize};
use solve_type::SolveType;
use std::collections::BTreeSet;
use std::collections::{HashMap, HashSet};
use std::convert::From;
use std::fmt;
use std::sync::{Arc, Mutex};

use salt::subdivision_code::SubdivisionCode;

type Fraction = GenericFraction<i32>;

#[derive(Deserialize)]
pub struct PossibleValues {
    pub jurisdictions: HashMap<Alpha2, HashSet<SubdivisionCode>>,
    pub amounts: HashSet<Fraction>,
    pub amts: HashSet<Fraction>,
    pub amount_to_customers: HashSet<Fraction>,
    pub min_amount: Fraction,
    pub max_amount: Fraction,
    pub min_amt: Fraction,
    pub max_amt: Fraction,
    pub min_amount_to_customer: Option<Fraction>,
    pub max_amount_to_customer: Option<Fraction>,
    pub aprs: HashSet<Fraction>,
    pub terms: HashSet<Fraction>,
    pub min_term: Fraction,
    pub max_term: Fraction,
    pub fees: HashSet<Fraction>,
    pub amount_scale: i32,
    pub amt_scale: i32,
    pub amount_to_customer_scale: i32,
    pub apr_scale: i32,
    pub term_scale: i32,
    pub fee_scale: i32,
}

lazy_static! {
    pub static ref MS_PER_SEC: BigFrac = BigFrac::from(1000.0);
    pub static ref SOLVER: Arc<Mutex<Solver>> = Arc::new(Mutex::new(Solver::default()));
    pub static ref MS_PER_MIN: BigFrac = *MS_PER_SEC * BigFrac::from(60.0);
    pub static ref MS_PER_HOUR: BigFrac = *MS_PER_MIN * BigFrac::from(60.0);
    pub static ref MS_PER_DAY: BigFrac = *MS_PER_HOUR * BigFrac::from(24.0);
    pub static ref MS_PER_MONTH: BigFrac = *MS_PER_DAY * BigFrac::from(30.0);
    pub static ref _MS_PER_YEAR: BigFrac = *MS_PER_MONTH * BigFrac::from(12.0);
    pub static ref FORMULATION: String = std::fs::read_to_string("./formulation/formulation.lp").expect("Opening ./formulation/formulation.lp");
    pub static ref RULES: String = std::fs::read_to_string("./formulation/rules.lp").expect("Opening ./formulation/rules.lp");
    pub static ref CACHE: Mutex<LruCache<LoanRequest, Option<Vec<ValueConstraint>>>> = Mutex::new(LruCache::new(1_000_000)); // 100MB
    pub static ref POSSIBLE_VALUES: PossibleValues = serde_cbor::from_reader(std::fs::File::open("./formulation/metadata.cbor").expect("Opening ./formulation/metadata.cbor")).unwrap();
    pub static ref AMOUNT_TREE: BTreeSet<i32> = POSSIBLE_VALUES.amounts
        .iter()
        .map(|amt| {
            assert!(*amt.numer().unwrap() as f64 * (POSSIBLE_VALUES.amount_scale / *amt.denom().unwrap()) as f64 <= std::i32::MAX as f64);
            *amt.numer().unwrap() * (POSSIBLE_VALUES.amount_scale / *amt.denom().unwrap())
        })
        .collect();
    pub static ref LOAN_AMOUNT_TREE_FOR_FEE: BTreeSet<i32> = POSSIBLE_VALUES.amts
        .iter()
        .map(|a| {
            assert!(*a.numer().unwrap() as f64 * (POSSIBLE_VALUES.amt_scale / *a.denom().unwrap()) as f64 <= std::i32::MAX as f64);
            *a.numer().unwrap() * (POSSIBLE_VALUES.amt_scale / *a.denom().unwrap())
        })
        .collect();
    pub static ref TERM_TREE: BTreeSet<i32> = POSSIBLE_VALUES.terms
        .iter()
        .map(|term| {
            assert!(*term.numer().unwrap() as f64 * (POSSIBLE_VALUES.term_scale / *term.denom().unwrap()) as f64 <= std::i32::MAX as f64);
            *term.numer().unwrap() * (POSSIBLE_VALUES.term_scale / *term.denom().unwrap())})
        .collect();
    pub static ref APR_TREE: BTreeSet<i32> = POSSIBLE_VALUES.aprs
        .iter()
        .map(|apr| {
            assert!(*apr.numer().unwrap() as f64 * (POSSIBLE_VALUES.apr_scale / *apr.denom().unwrap()) as f64 <= std::i32::MAX as f64);
            *apr.numer().unwrap() * (POSSIBLE_VALUES.apr_scale / *apr.denom().unwrap())
        })
        .collect();
    pub static ref FEE_TREE: BTreeSet<i32> = POSSIBLE_VALUES.fees
        .iter()
        .map(|fee| {
            assert!(*fee.numer().unwrap() as f64 * (POSSIBLE_VALUES.fee_scale / *fee.denom().unwrap()) as f64 <= std::i32::MAX as f64);
            *fee.numer().unwrap() * (POSSIBLE_VALUES.fee_scale / *fee.denom().unwrap())
        })
    .collect();
    pub static ref AMOUNT_TO_CUSTOMER_TREE: BTreeSet<i32> = POSSIBLE_VALUES.amount_to_customers
        .iter()
        .map(|amt| {
            assert!(*amt.numer().unwrap() as f64 * (POSSIBLE_VALUES.amount_to_customer_scale / *amt.denom().unwrap()) as f64 <= std::i32::MAX as f64);
            *amt.numer().unwrap() * (POSSIBLE_VALUES.amount_to_customer_scale / *amt.denom().unwrap())
        })
        .collect();

    static ref JUR_SET: HashMap<Alpha2, CountryLendability> = {
        let possibilities: &HashMap<Alpha2, HashSet<SubdivisionCode>> = &POSSIBLE_VALUES.jurisdictions;
        let mut solver = SOLVER.lock().unwrap();
        possibilities.iter().map(|(country, provinces)| {
            (
                *country,
                CountryLendability {
                    default: match (solver.solve(SolveType::SAT, LoanInstance {
                        country: *country,
                        province: None,
                        loan_type: LoanType::Business,
                        loan_interest_type: None,
                        amount: None,
                        apr: None,
                        term: None,
                        fee: None,
                        amount_to_customer: None,
                    }, QueryType::Legality).unwrap(), solver.solve(SolveType::SAT, LoanInstance {
                        country: *country,
                        province: None,
                        loan_type: LoanType::Personal,
                        loan_interest_type: None,
                        amount: None,
                        apr: None,
                        term: None,
                        fee: None,
                        amount_to_customer: None,
                    }, QueryType::Legality).unwrap()) {
                        (Some(_), Some(_)) => LendabilityType::Lendable,
                        (Some(_), None) => LendabilityType::BusinessOnly,
                        (None, Some(_)) => LendabilityType::PersonalOnly,
                        (None, None) => LendabilityType::NonLendable,
                    },
                    provinces: provinces.iter().map(|province| {
                        (
                            province.clone(),
                            match (solver.solve(SolveType::SAT, LoanInstance {
                                country: *country,
                                province: Some(province.clone()),
                                loan_type: LoanType::Business,
                                loan_interest_type: None,
                                amount: None,
                                apr: None,
                                term: None,
                                fee: None,
                                amount_to_customer: None,
                            }, QueryType::Legality).unwrap(), solver.solve(SolveType::SAT, LoanInstance {
                                country: *country,
                                province: Some(province.clone()),
                                loan_type: LoanType::Personal,
                                loan_interest_type: None,
                                amount: None,
                                apr: None,
                                term: None,
                                fee: None,
                                amount_to_customer: None,
                            }, QueryType::Legality).unwrap()) {
                                (Some(_), Some(_)) => LendabilityType::Lendable,
                                (Some(_), None) => LendabilityType::BusinessOnly,
                                (None, Some(_)) => LendabilityType::PersonalOnly,
                                (None, None) => LendabilityType::NonLendable,
                            }
                        )
                    }).collect()
                }
            )
        }).collect()
    };
}

#[derive(Serialize)]
#[serde(rename_all = "snake_case")]
enum LendabilityType {
    BusinessOnly,
    PersonalOnly,
    Lendable,
    NonLendable,
}

#[derive(Serialize)]
struct CountryLendability {
    default: LendabilityType,
    #[serde(flatten)]
    provinces: HashMap<SubdivisionCode, LendabilityType>,
}

#[derive(Deserialize, Debug, PartialEq, Eq, Hash, Clone)]
#[serde(rename_all = "lowercase")]
pub enum LoanType {
    Business,
    Personal,
}
impl fmt::Display for LoanType {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        match self {
            LoanType::Business => write!(f, "business"),
            LoanType::Personal => write!(f, "personal"),
        }
    }
}
#[derive(Deserialize, Debug, PartialEq, Eq, Hash, Clone)]
#[serde(rename_all = "lowercase")]
pub enum LoanInterestType {
    // Principal and interest
    PNI,
    // Interest only
    IO,
}
impl fmt::Display for LoanInterestType {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        match self {
            LoanInterestType::PNI => write!(f, "PNI"),
            LoanInterestType::IO => write!(f, "IO"),
        }
    }
}

pub enum ConstraintType {
    Min,
    Max,
}

fn ser_frac<S>(f: &BigFrac, s: S) -> Result<S::Ok, S::Error>
where
    S: serde::Serializer,
{
    s.serialize_str(&format!("{:.1$}", f, 32))
}

#[derive(Debug, Serialize, Clone)]
#[serde(tag = "constraint", content = "value")]
pub enum ValueConstraint {
    UNK,
    LT(#[serde(serialize_with = "ser_frac")] BigFrac),
    LTE(#[serde(serialize_with = "ser_frac")] BigFrac),
    GT(#[serde(serialize_with = "ser_frac")] BigFrac),
    GTE(#[serde(serialize_with = "ser_frac")] BigFrac),
}

#[derive(Deserialize, Debug, PartialEq, Eq, Hash, Clone, Copy)]
#[serde(rename_all = "snake_case")]
pub enum QueryType {
    Apr,
    Amount,
    Term,
    Legality,
    Fee,
}

#[derive(Deserialize, Debug, PartialEq, Eq, Hash)]
pub struct LoanRequest {
    pub loan: LoanInstance,
    pub query: Option<QueryType>,
}

#[derive(Serialize)]
struct StatusResponse {
    pub status: String,
}

fn to_amount_int(amount: BigFrac) -> i32 {
    let mut scaled = *amount.numer().unwrap() as f64
        * (POSSIBLE_VALUES.amount_scale as f64 / *amount.denom().unwrap() as f64);
    if scaled > std::i32::MAX as f64 {
        scaled = std::i32::MAX as f64;
    }
    let smallest_gte = AMOUNT_TREE
        .range((scaled.ceil() as i32)..)
        .next()
        .copied()
        .unwrap_or_else(|| AMOUNT_TREE.iter().next_back().unwrap() + 2);
    if (smallest_gte as f64 - scaled).abs() < 0.01 {
        smallest_gte
    } else {
        smallest_gte - 1
    }
}
fn to_amt_int(amt: BigFrac) -> i32 {
    let mut scaled = *amt.numer().unwrap() as f64
        * (POSSIBLE_VALUES.amt_scale as f64 / *amt.denom().unwrap() as f64);
    if scaled > std::i32::MAX as f64 {
        scaled = std::i32::MAX as f64;
    }
    let smallest_gte = LOAN_AMOUNT_TREE_FOR_FEE
        .range((scaled.ceil() as i32)..)
        .next()
        .copied()
        .unwrap_or_else(|| LOAN_AMOUNT_TREE_FOR_FEE.iter().next_back().unwrap() + 2);
    if (smallest_gte as f64 - scaled).abs() < 0.01 {
        smallest_gte
    } else {
        smallest_gte - 1
    }
}
fn to_apr_int(apr: BigFrac) -> i32 {
    let mut scaled = *apr.numer().unwrap() as f64
        * (POSSIBLE_VALUES.apr_scale as f64 / (*apr.denom().unwrap() as f64 / 100.0));
    if scaled > std::i32::MAX as f64 {
        scaled = std::i32::MAX as f64;
    }
    let smallest_gte = APR_TREE
        .range((scaled.ceil() as i32)..)
        .next()
        .copied()
        .unwrap_or_else(|| APR_TREE.iter().next_back().unwrap() + 2);
    if (smallest_gte as f64 - scaled).abs() < 0.01 {
        smallest_gte
    } else {
        smallest_gte - 1
    }
}
fn to_fee_int(fee: BigFrac) -> i32 {
    let mut scaled = *fee.numer().unwrap() as f64
        * (POSSIBLE_VALUES.fee_scale as f64 / *fee.denom().unwrap() as f64);
    if scaled > std::i32::MAX as f64 {
        scaled = std::i32::MAX as f64;
    }
    let candidate = FEE_TREE
        .range(..=(scaled.floor() as i32))
        .next_back()
        .copied()
        .unwrap_or_else(|| *FEE_TREE.iter().next().unwrap());
    candidate
}

/// Converts a BigFrac amount_to_customer value to scaled integer for ASP processing
fn to_amount_to_customer_int(amount_to_customer: BigFrac) -> i32 {
    let mut scaled = *amount_to_customer.numer().unwrap() as f64
        * (POSSIBLE_VALUES.amount_to_customer_scale as f64 / *amount_to_customer.denom().unwrap() as f64);
    if scaled > std::i32::MAX as f64 {
        scaled = std::i32::MAX as f64;
    }
    let candidate = AMOUNT_TO_CUSTOMER_TREE
        .range(..=(scaled.floor() as i32))
        .next_back()
        .copied()
        .unwrap_or_else(|| *AMOUNT_TO_CUSTOMER_TREE.iter().next().unwrap());
    candidate
}

pub fn legality(
    body: LoanRequest,
    solver: &mut Solver,
) -> Result<Option<Vec<ValueConstraint>>, Error> {
    let mut cache = CACHE.lock().unwrap();
    let (cached, result) = match cache.get_mut(&body) {
        Some(cached_result) => (true, cached_result.clone()),
        None => {
            let computed_result = match body.query {
                Some(QueryType::Amount) => {
                    solve_min_max_constraints(solver, &body.loan, SolveType::MIN_AMOUNT, SolveType::MAX_AMOUNT, QueryType::Amount)?
                }
                Some(QueryType::Apr) => {
                    solve_min_max_constraints(solver, &body.loan, SolveType::MIN_APR, SolveType::MAX_APR, QueryType::Apr)?
                }
                Some(QueryType::Term) => {
                    solve_min_max_constraints(solver, &body.loan, SolveType::MIN_TERM, SolveType::MAX_TERM, QueryType::Term)?
                }
                Some(QueryType::Fee) => {
                    solve_min_max_constraints(solver, &body.loan, SolveType::MIN_FEE, SolveType::MAX_FEE, QueryType::Fee)?
                }
                Some(QueryType::Legality) | None => {
                    match solver.solve(SolveType::SAT, body.loan.clone(), QueryType::Legality)? {
                        Some(_) => Some(vec![]),
                        None => None,
                    }
                }
            };
            (false, computed_result)
        }
    };
    
    if !cached {
        cache.insert(body, result.clone());
    }
    Ok(result)
}

fn solve_min_max_constraints(
    solver: &mut Solver,
    loan: &LoanInstance,
    min_solve_type: SolveType,
    max_solve_type: SolveType,
    query_type: QueryType,
) -> Result<Option<Vec<ValueConstraint>>, Error> {
    match (
        solver.solve(min_solve_type, loan.clone(), query_type)?,
        solver.solve(max_solve_type, loan.clone(), query_type)?,
    ) {
        (Some(min_constraint), Some(max_constraint)) => Ok(Some(vec![min_constraint, max_constraint])),
        _ => Ok(None),
    }
}

pub fn validate_loanrequest(request: &LoanRequest) -> Result<(), Error> {
    match request.query {
        Some(QueryType::Amount) => {
            if request.loan.amount.is_some() {
                bail!("Cannot query amount when amount is specified.")
            }
            if request.loan.fee.is_some() {
                bail!("Cannot query amount when fee is specified.")
            }
        }
        Some(QueryType::Apr) => {
            if request.loan.apr.is_some() {
                bail!("Cannot query apr when apr is specified.")
            }
            if request.loan.fee.is_some() {
                bail!("Cannot query apr when fee is specified.")
            }
        }
        Some(QueryType::Term) => {
            if request.loan.term.is_some() {
                bail!("Cannot query term when term is specified.")
            }
            if request.loan.fee.is_some() {
                bail!("Cannot query term when fee is specified.")
            }
        }
        Some(QueryType::Fee) => {
            if request.loan.fee.is_some() {
                bail!("Cannot query fee when fee is specified.")
            }
        }
        Some(QueryType::Legality) | None => {
            // No validation needed for legality queries
        }
    }
    Ok(())
}

#[get("/status/shallow")]
fn get_status_shallow() -> Json<StatusResponse> {
    StatusResponse {
        status: "OK".to_owned(),
    }
    .into()
}

#[get("/status/deep")]
fn get_status_deep() -> Json<StatusResponse> {
    StatusResponse {
        status: "OK".to_owned(),
    }
    .into()
}

#[post("/v0/legality")]
async fn post_legality(
    #[data] solver: Arc<Mutex<Solver>>,
    #[json] body: LoanRequest,
) -> Result<Json<Option<Vec<ValueConstraint>>>, Rejection> {
    if let Err(e) = validate_loanrequest(&body) {
        return Err(AnyhowError::anyhow_into_rejection(e));
    }
    
    match legality(body, &mut solver.lock().unwrap()) {
        Ok(result) => Ok(result.into()),
        Err(_) => Ok((None as Option<Vec<ValueConstraint>>).into()), // On any error, return 200 OK with null body
    }
}

#[get("/status/git")]
async fn get_status_git() -> Result<Json<String>, Rejection> {
    use std::io::Read;
    let mut commit_hash = String::new();
    let mut f = std::fs::File::open("gitCommit.out").map_err(AnyhowError::anyhow_into_rejection)?;
    f.read_to_string(&mut commit_hash)
        .map_err(AnyhowError::anyhow_into_rejection)?;
    Ok(commit_hash.into())
}

#[get("/v0/all")]
fn get_all() -> Json<&'static HashMap<Alpha2, CountryLendability>> {
    Json::from(&*JUR_SET)
}

#[tokio::main]
async fn main() -> Result<(), Error> {
    use std::env;
    let args: Vec<String> = env::args().collect();
    let port = args
        .get(1)
        .cloned()
        .unwrap_or_else(|| "3000".to_string())
        .parse()
        .expect("valid port number");
    if env::var_os("RUST_LOG").is_none() {
        env::set_var("RUST_LOG", "web=info,warn");
    }
    pretty_env_logger::init();

    println!("Loading Jurisdictional Data");
    lazy_static::initialize(&JUR_SET);

    println!("Jurisdictional Data Loaded");
    println!("Amount scale: {}", POSSIBLE_VALUES.amount_scale);
    println!("Amount tree sample values: {:?}", AMOUNT_TREE.iter().take(10).collect::<Vec<_>>());
    let routes = get_status_shallow()
        .or(get_status_deep())
        .or(get_status_git())
        .or(post_legality(SOLVER.clone()))
        .or(get_all());
    serve(
        routes
            .with(rweb::warp::log("web"))
            .recover(warp_rejection::handle_rejection),
    )
    .run(([0, 0, 0, 0], port))
    .await;

    Ok(())
}
