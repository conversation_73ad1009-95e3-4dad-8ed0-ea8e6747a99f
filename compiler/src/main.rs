use anyhow::{bail, format_err, Error, Result};
use fmt::Debug;
use fraction::GenericFraction;
use pest::iterators::{Pair, Pairs};
use pest::Parser;
use pest_derive::Parser;
use serde::Serialize;
use std::collections::{HashMap, HashSet};
use std::fmt;
use std::fs::File;
use std::io::Write;

type Fraction = GenericFraction<i32>;

#[derive(Debug, Clone, Eq, PartialEq)]
enum Interest {
    // Principle and interest
    PNI,
    // Interest only
    IO,
}
impl fmt::Display for Interest {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        match self {
            Interest::PNI => write!(f, "PNI"),
            Interest::IO => write!(f, "IO"),
        }
    }
}

fn valid_option_and<T: Sized + Eq + std::fmt::Debug>(
    left: Option<T>,
    right: Option<T>,
) -> Result<Option<T>> {
    Ok(match (left, right) {
        (None, None) => None,
        (Some(a), None) => Some(a),
        (None, Some(a)) => Some(a),
        (Some(a), Some(b)) if a == b => Some(a),
        (Some(a), Some(b)) => bail!("expression contains a contradiction: {:?} != {:?}", a, b),
    })
}

#[derive(Parser)]
#[grammar = "lendable.pest"]
struct LendableParser;

#[derive(Debug, Serialize)]
struct PossibleValues {
    pub jurisdictions: HashMap<String, HashSet<String>>,
    pub amounts: HashSet<Fraction>,
    pub amts: HashSet<Fraction>,
    pub amount_to_customers: HashSet<Fraction>,
    pub min_amount: Option<Fraction>,
    pub max_amount: Option<Fraction>,
    pub min_amt: Option<Fraction>,
    pub max_amt: Option<Fraction>,
    pub min_amount_to_customer: Option<Fraction>,
    pub max_amount_to_customer: Option<Fraction>,
    pub aprs: HashSet<Fraction>,
    pub terms: HashSet<Fraction>,
    pub min_term: Option<Fraction>,
    pub max_term: Option<Fraction>,
    pub fees: HashSet<Fraction>,
    pub amount_scale: i32,
    pub amt_scale: i32,
    pub amount_to_customer_scale: i32,
    pub apr_scale: i32,
    pub term_scale: i32,
    pub fee_scale: i32,
}
impl PossibleValues {
    pub fn new() -> Self {
        PossibleValues {
            jurisdictions: HashMap::new(),
            amounts: HashSet::new(),
            amts: HashSet::new(),
            amount_to_customers: [Fraction::from(0.0)].iter().cloned().collect(),
            min_amount: None,
            max_amount: None,
            min_amt: None,
            max_amt: None,
            min_amount_to_customer: None,
            max_amount_to_customer: None,
            aprs: [Fraction::from(0.0), Fraction::from(100.0)]
                .iter()
                .cloned()
                .collect(),
            terms: HashSet::new(),
            min_term: None,
            max_term: None,
            fees: [Fraction::from(0.0)].iter().cloned().collect(),
            amount_scale: 0,
            amt_scale: 0,
            amount_to_customer_scale: 0,
            apr_scale: 0,
            term_scale: 0,
            fee_scale: 0,
        }
    }
}

#[derive(Clone, Debug)]
struct ExprHandle {
    pub handle: String,
    pub country: Option<String>,
    pub province: HashSet<String>,
    pub contains_personal: bool,
    pub contains_business: bool,
    pub interest: Option<Interest>,
    pub atomic: bool,
}
impl ExprHandle {
    fn new(handle: String, atomic: bool) -> ExprHandle {
        ExprHandle {
            handle,
            country: None,
            province: HashSet::new(),
            contains_business: false,
            contains_personal: false,
            interest: None,
            atomic,
        }
    }
}
impl fmt::Display for ExprHandle {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        write!(f, "{}", self.handle)
    }
}

#[derive(Copy, Clone)]
enum BoolOp {
    And,
    Or,
}

struct CompileContext {
    emit: bool,
    line: usize,
    scopeused: bool,
    scope: Vec<ExprHandle>,
    expr_index: usize,
    const_index: usize,
}
impl CompileContext {
    fn new(emit: bool) -> CompileContext {
        CompileContext {
            emit,
            line: 1,
            scopeused: true,
            scope: Vec::new(),
            expr_index: 0,
            const_index: 0,
        }
    }
    fn incr_line(&mut self) {
        self.line += 1;
    }
    fn set_tablevel(&mut self, level: usize) -> Result<(), Error> {
        use std::cmp::Ordering;
        match level.cmp(&self.scope.len()) {
            Ordering::Greater => Err(format_err!("cannot increase tab level with set_tablevel")),
            Ordering::Less => {
                if !self.scopeused {
                    Err(format_err!(
                        "FOR expression must contain at least one statement!"
                    ))
                } else {
                    for _ in 0..(self.scope.len() - level) {
                        self.scope.pop();
                    }
                    Ok(())
                }
            }
            Ordering::Equal => {
                self.scopeused = true;
                Ok(())
            }
        }
    }
    fn push_scope(&mut self, expr: ExprHandle) {
        self.scopeused = false;
        self.scope.push(expr);
    }
    fn get_expr_handle(&mut self) -> ExprHandle {
        let val = self.expr_index;
        self.expr_index = val + 1;
        ExprHandle {
            handle: format!("expr({})", val),
            country: None,
            province: HashSet::new(),
            contains_business: false,
            contains_personal: false,
            interest: None,
            atomic: true,
        }
    }
    fn get_const_idx(&mut self) -> usize {
        let val = self.const_index;
        self.const_index += 1;
        val
    }
    fn emit(&self) -> bool {
        self.emit
    }
}

fn parse_fee(pair: Pair<'_, Rule>, metadata: &mut PossibleValues) -> Result<Fraction, Error> {
    let mut num: Option<&str> = None;
    let mut unit: Option<&str> = None;
    for subpair in pair.into_inner() {
        match subpair.as_rule() {
            Rule::number => num = Some(subpair.clone().as_span().as_str()),
            Rule::fee_unit => unit = Some(subpair.clone().as_span().as_str()),
            a => return Err(format_err!("{:?} is not a valid subtoken of fee", a)),
        }
    }

    let num = num.ok_or_else(|| format_err!("fee must contain a number"))?;
    let val = num.parse::<f64>()?;
    let res = match unit {
        Some(u) => match u {
            "k" => Ok(Fraction::from(val)),
            "m" => Ok(Fraction::from(val * 1000.0)),
            _ => Err(format_err!("Could not parse unit: {}: must be k or m", u)),
        },
        None => Ok(Fraction::from(val / 1000.0)),
    }?;

    metadata.fees.insert(res.clone());

    Ok(res)
}

fn parse_amount(
    pair: Pair<'_, Rule>,
    metadata: &mut PossibleValues,
    is_amount_from_fee_expr: bool,
) -> Result<Fraction, Error> {
    let mut num: Option<&str> = None;
    let mut unit: Option<&str> = None;
    for subpair in pair.into_inner() {
        match subpair.as_rule() {
            Rule::number => num = Some(subpair.clone().as_span().as_str()),
            Rule::amount_unit => unit = Some(subpair.clone().as_span().as_str()),
            a => return Err(format_err!("{:?} is not a valid subtoken of amount", a)),
        }
    }
    let num = num.ok_or_else(|| format_err!("amount must contain a number"))?;
    let unit = unit.ok_or_else(|| format_err!("amount must contain a unit"))?;
    let val = num.parse::<f64>()?;
    let res = match unit {
        "k" => Ok(Fraction::from(val * 1000.0)),
        "m" => Ok(Fraction::from(val * 1000000.0)),
        _ => Err(format_err!(
            "Could not parse unit: {}: must be k or m",
            unit
        )),
    }?;

    // every parsed amount should be inserted into amts to mirror amounts, but specific amounts from fee expresions do not go into the regular amounts metadata to not mess with the bounds
    if !is_amount_from_fee_expr {
        metadata.amounts.insert(res.clone());
    }

    metadata.amts.insert(res.clone());

    Ok(res)
}

fn parse_percentage(
    pair: Pair<'_, Rule>,
    metadata: &mut PossibleValues,
) -> Result<Fraction, Error> {
    let mut num: Option<&str> = None;
    for subpair in pair.into_inner() {
        match subpair.as_rule() {
            Rule::number => num = Some(subpair.clone().as_span().as_str()),
            a => return Err(format_err!("{:?} is not a valid subtoken of percentage", a)),
        }
    }
    let num = num.ok_or_else(|| format_err!("percentage must contain a number"))?;
    let val = num.parse::<f64>()?;
    let res = Fraction::from(val);

    metadata.aprs.insert(res.clone());

    Ok(res)
}

fn parse_time(pair: Pair<'_, Rule>, metadata: &mut PossibleValues) -> Result<Fraction, Error> {
    let mut num: Option<&str> = None;
    let mut unit: Option<&str> = None;
    for subpair in pair.into_inner() {
        match subpair.as_rule() {
            Rule::number => num = Some(subpair.clone().as_span().as_str()),
            Rule::time_unit => unit = Some(subpair.clone().as_span().as_str()),
            a => return Err(format_err!("{:?} is not a valid subtoken of amount", a)),
        }
    }
    let num = num.ok_or_else(|| format_err!("time must contain a number"))?;
    let unit = unit.ok_or_else(|| format_err!("time must contain a unit"))?;
    let val = num.parse::<f64>()?;
    let res = match unit {
        "mo" => Ok(Fraction::from(val)),
        "yr" => Ok(Fraction::from(val * 12.0)),
        _ => Err(format_err!(
            "Could not parse unit: {}: must be mo or yr",
            unit
        )),
    }?;

    metadata.terms.insert(res.clone());

    Ok(res)
}

fn compile_amount_range(
    ctx: &CompileContext,
    outfile: &mut File,
    pairs: Pairs<'_, Rule>,
    metadata: &mut PossibleValues,
) -> Result<(), Error> {
    let mut range_vec: Vec<Fraction> = Vec::new();
    for pair in pairs {
        match pair.as_rule() {
            Rule::amount => {
                range_vec.push(parse_amount(pair, metadata, false)?);
            }
            a => {
                return Err(format_err!(
                    "{:?} is not a valid subtoken of amount_range",
                    a
                ))
            }
        }
    }
    let min = range_vec
        .get(0)
        .ok_or_else(|| format_err!("amount_range must contain 2 amounts"))?;
    let max = range_vec
        .get(1)
        .ok_or_else(|| format_err!("amount_range must contain 2 amounts"))?;
    metadata.min_amount = Some(*min);
    metadata.max_amount = Some(*max);

    // mirror the range of reg loan amounts for this fee specific field
    metadata.min_amt = Some(*min);
    metadata.max_amt = Some(*max);

    // set an upper-bound fee equal to the maximum loan amount.  Fee literals are
    // interpreted in thousands-of-dollars, so convert the dollar amount to that
    // scale before inserting.  (max is guaranteed to be an integer after unit
    // expansion, so the division is exact.)
    metadata
        .fees
        .insert(Fraction::from(*max.numer().unwrap() as f64 / 1000.0));

    if min > max {
        return Err(format_err!("minimum of range must less than maximum"));
    }

    if ctx.emit() {
        outfile.write_all(":- amount(A), not amountrange(A).\n".to_string().as_bytes())?;
        outfile.write_all(":- not amount(_).\n".to_string().as_bytes())?;
        outfile.write_all(
            "#external amount_ext(A) : amountrange(A).\n"
                .to_string()
                .as_bytes(),
        )?;
        outfile.write_all("amount(A) :- amount_ext(A).\n".to_string().as_bytes())?;

        // write to separate variable of amt purely for fee stmt's as we do not want the amount bound to be lowered by any possible fee rule
        outfile.write_all(
            ":- amt(A), not amtrange(A).\n"
                .to_string()
                .as_bytes(),
        )?;
        outfile.write_all(":- not amt(_).\n".to_string().as_bytes())?;
        outfile.write_all(
            "#external amt_ext(A) : amtrange(A).\n"
                .to_string()
                .as_bytes(),
        )?;
        outfile.write_all(
            "amt(A) :- amt_ext(A).\n"
                .to_string()
                .as_bytes(),
        )?;
    }
    Ok(())
}

fn compile_term_range(
    ctx: &CompileContext,
    outfile: &mut File,
    pairs: Pairs<'_, Rule>,
    metadata: &mut PossibleValues,
) -> Result<(), Error> {
    let mut range_vec: Vec<Fraction> = Vec::new();
    for pair in pairs {
        match pair.as_rule() {
            Rule::time => {
                range_vec.push(parse_time(pair, metadata)?);
            }
            a => return Err(format_err!("{:?} is not a valid subtoken of time_range", a)),
        }
    }
    let min = range_vec
        .get(0)
        .ok_or_else(|| format_err!("term_range must contain 2 amounts"))?;
    let max = range_vec
        .get(1)
        .ok_or_else(|| format_err!("term_range must contain 2 amounts"))?;
    metadata.min_term = Some(*min);
    metadata.max_term = Some(*max);
    if min > max {
        return Err(format_err!("minimum of range must less than maximum"));
    }
    if ctx.emit() {
        outfile.write_all(&":- term(A), not termrange(A).\n".to_string().as_bytes())?;
        outfile.write_all(&":- not term(_).\n".to_string().as_bytes())?;
        outfile.write_all(
            &"#external term_ext(A) : termrange(A).\n"
                .to_string()
                .as_bytes(),
        )?;
        outfile.write_all(&"term(A) :- term_ext(A).\n".to_string().as_bytes())?;
    }
    Ok(())
}

fn compile_newline(ctx: &mut CompileContext, pair: Pair<'_, Rule>) -> Result<(), Error> {
    match pair.as_rule() {
        Rule::newline => {
            ctx.incr_line();
            let mut tab_num = 0;
            for pair in pair.into_inner() {
                match pair.as_rule() {
                    Rule::tab => {
                        tab_num += 1;
                    }
                    a => return Err(format_err!("expect tab found {:?}", a)),
                }
            }
            ctx.set_tablevel(tab_num)?;
            Ok(())
        }
        a => Err(format_err!("expected newline found {:?}", a)),
    }
}

fn compile_percentage_comp_expr(
    ctx: &mut CompileContext,
    pairs: Pairs<'_, Rule>,
    metadata: &mut PossibleValues,
) -> Result<ExprHandle, Error> {
    let pairs_vec: Vec<Pair<'_, Rule>> = pairs.collect();
    let percentage_var = pairs_vec
        .get(0)
        .ok_or_else(|| format_err!("percentage comp expr is empty"))?
        .clone()
        .as_span()
        .as_str();
    let comp_op = pairs_vec
        .get(1)
        .ok_or_else(|| format_err!("percentage comp expr is missing operator"))?
        .clone()
        .as_span()
        .as_str();
    let percentage_pair = pairs_vec
        .get(2)
        .ok_or_else(|| format_err!("percentage comp expr is missing value"))?;
    let amount = match percentage_pair.as_rule() {
        Rule::percentage => {
            let frac = parse_percentage(percentage_pair.clone(), metadata)?;
            format!(
                "{}",
                frac.numer().unwrap() * (metadata.apr_scale / frac.denom().unwrap()),
            )
        }
        Rule::percentagevar => {
            let idx = ctx.get_const_idx();
            format!(
                "B{}, {}(B{}), aprrange(B{})",
                idx,
                percentage_pair.clone().as_span().as_str(),
                idx,
                idx,
            )
        }
        a => return Err(format_err!("expected percentageval got {:?}", a)),
    };
    let handle = ExprHandle::new(
        format!(
            "{}({}), {} {} {}",
            percentage_var,
            percentage_var.to_uppercase(),
            percentage_var.to_uppercase(),
            comp_op,
            amount
        ),
        false,
    );
    Ok(handle)
}

fn compile_amount_comp_expr(
    ctx: &mut CompileContext,
    pairs: Pairs<'_, Rule>,
    metadata: &mut PossibleValues,
    is_comp_expr_from_fee_stmt: bool,
) -> Result<ExprHandle, Error> {
    let pairs_vec: Vec<Pair<'_, Rule>> = pairs.collect();
    let amount_var = pairs_vec
        .get(0)
        .ok_or_else(|| format_err!("amount comp expr is empty"))?
        .clone()
        .as_span()
        .as_str();
    let comp_op = pairs_vec
        .get(1)
        .ok_or_else(|| format_err!("amount comp expr is missing operator"))?
        .clone()
        .as_span()
        .as_str();
    let amount_pair = pairs_vec
        .get(2)
        .ok_or_else(|| format_err!("amount comp expr is missing value"))?;
    let amount = match amount_pair.as_rule() {
        Rule::amount => {
            let frac = parse_amount(amount_pair.clone(), metadata, is_comp_expr_from_fee_stmt)?;
            let scale = if is_comp_expr_from_fee_stmt {
                metadata.amt_scale
            } else {
                metadata.amount_scale
            };
            format!(
                "{}",
                frac.numer().unwrap() * (scale / frac.denom().unwrap()),
            )
        }
        Rule::amountvar => {
            let idx = ctx.get_const_idx();
            let amount_pair_str = amount_pair.clone().as_span().as_str();
            format!(
                "B{}, {}(B{}), {}range(B{})",
                idx, amount_pair_str, idx, amount_pair_str, idx
            )
        }
        a => return Err(format_err!("expected amountval got {:?}", a)),
    };
    let handle = if is_comp_expr_from_fee_stmt {
        let new_amount_var = if amount_var == "amount" {
            "amt"
        } else {
            amount_var
        };

        ExprHandle::new(
            format!(
                "{}({}), {} {} {}",
                new_amount_var,
                new_amount_var.to_uppercase(),
                new_amount_var.to_uppercase(),
                comp_op,
                amount
            ),
            false,
        )
    } else {
        ExprHandle::new(
            format!(
                "{}({}), {} {} {}",
                amount_var,
                amount_var.to_uppercase(),
                amount_var.to_uppercase(),
                comp_op,
                amount
            ),
            false,
        )
    };
    Ok(handle)
}

fn compile_fee_comp_expr(
    ctx: &mut CompileContext,
    pairs: Pairs<'_, Rule>,
    metadata: &mut PossibleValues,
) -> Result<ExprHandle, Error> {
    let pairs_vec: Vec<Pair<'_, Rule>> = pairs.collect();
    let fee_var = pairs_vec
        .get(0)
        .ok_or_else(|| format_err!("fee comp expr is empty"))?
        .clone()
        .as_span()
        .as_str();
    let comp_op = pairs_vec
        .get(1)
        .ok_or_else(|| format_err!("fee comp expr is missing operator"))?
        .clone()
        .as_span()
        .as_str();

    let fee_pair = pairs_vec
        .get(2)
        .ok_or_else(|| format_err!("fee comp expr is missing value"))?;
    let fee = match fee_pair.as_rule() {
        Rule::fee => {
            let frac = parse_fee(fee_pair.clone(), metadata)?;
            format!(
                "{}",
                frac.numer().unwrap() * (metadata.fee_scale / frac.denom().unwrap()),
            )
        }
        Rule::feevar => {
            let idx = ctx.get_const_idx();
            let fee_pair_str = fee_pair.clone().as_span().as_str();
            format!(
                "B{}, {}(B{}), {}range(B{})",
                idx, fee_pair_str, idx, fee_pair_str, idx
            )
        }
        a => return Err(format_err!("expected feeval got {:?}", a)),
    };
    let handle = ExprHandle::new(
        format!(
            "{}({}), {} {} {}",
            fee_var,
            fee_var.to_uppercase(),
            fee_var.to_uppercase(),
            comp_op,
            fee
        ),
        false,
    );
    Ok(handle)
}

fn compile_time_comp_expr(
    ctx: &mut CompileContext,
    pairs: Pairs<'_, Rule>,
    metadata: &mut PossibleValues,
) -> Result<ExprHandle, Error> {
    let pairs_vec: Vec<Pair<'_, Rule>> = pairs.collect();
    let time_var = pairs_vec
        .get(0)
        .ok_or_else(|| format_err!("time comp expr is empty"))?
        .clone()
        .as_span()
        .as_str();
    let comp_op = pairs_vec
        .get(1)
        .ok_or_else(|| format_err!("time comp expr is missing operator"))?
        .clone()
        .as_span()
        .as_str();
    let time_pair = pairs_vec
        .get(2)
        .ok_or_else(|| format_err!("time comp expr is missing value"))?;
    let time = match time_pair.as_rule() {
        Rule::time => {
            let frac = parse_time(time_pair.clone(), metadata)?;
            format!(
                "{}",
                frac.numer().unwrap() * (metadata.term_scale / frac.denom().unwrap()),
            )
        }
        Rule::timevar => {
            let idx = ctx.get_const_idx();
            let time_pair_str = time_pair.clone().as_span().as_str();
            format!(
                "B{}, {}(B{}), {}range(B{})",
                idx, time_pair_str, idx, time_pair_str, idx
            )
        }
        a => return Err(format_err!("expected timeval got {:?}", a)),
    };
    let handle = ExprHandle::new(
        format!(
            "{}({}), {} {} {}",
            time_var,
            time_var.to_uppercase(),
            time_var.to_uppercase(),
            comp_op,
            time
        ),
        false,
    );
    Ok(handle)
}

fn compile_amount_to_customer_comp_expr(
    ctx: &mut CompileContext,
    pairs: Pairs<'_, Rule>,
    metadata: &mut PossibleValues,
) -> Result<ExprHandle, Error> {
    let pairs_vec: Vec<Pair<'_, Rule>> = pairs.collect();
    let amount_to_customer_var = pairs_vec
        .get(0)
        .ok_or_else(|| format_err!("amount_to_customer comp expr is empty"))?
        .clone()
        .as_span()
        .as_str();
    let comp_op = pairs_vec
        .get(1)
        .ok_or_else(|| format_err!("amount_to_customer comp expr is missing operator"))?
        .clone()
        .as_span()
        .as_str();
    let amount_pair = pairs_vec
        .get(2)
        .ok_or_else(|| format_err!("amount_to_customer comp expr is missing value"))?;
    let amount = match amount_pair.as_rule() {
        Rule::amount => {
            let frac = parse_amount(amount_pair.clone(), metadata, false)?;
            metadata.amount_to_customers.insert(frac.clone());
            // Use current (or provisional) scale to stay consistent with range generation.
            let scale = if metadata.amount_to_customer_scale == 0 {
                2 // denominators are integers; mirror logic used when scale computed later
            } else {
                metadata.amount_to_customer_scale
            };
            format!(
                "{}",
                frac.numer().unwrap() * (scale / *frac.denom().unwrap())
            )
        }

        Rule::amounttocustomervar => {
            let idx = ctx.get_const_idx();
            format!(
                "B{}, amount_to_customer(B{}), amount_to_customer_range(B{})",
                idx, idx, idx
            )
        }
        a => return Err(format_err!("expected amounttocustomerval got {:?}", a)),
    };
    let handle = ExprHandle::new(
        format!(
            "{}({}), {} {} {}",
            amount_to_customer_var,
            amount_to_customer_var.to_uppercase(),
            amount_to_customer_var.to_uppercase(),
            comp_op,
            amount
        ),
        false,
    );
    Ok(handle)
}

fn compile_jur_expr(
    pairs: &mut Pairs<'_, Rule>,
    metadata: &mut PossibleValues,
) -> Result<ExprHandle, Error> {
    let pair = pairs
        .next()
        .ok_or_else(|| format_err!("jur_expr is empty"))?;
    match pair.as_rule() {
        Rule::country_expr => {
            let jur_pair = pair
                .into_inner()
                .next()
                .ok_or_else(|| format_err!("country_expr is empty"))?;
            let country = jur_pair.as_span().as_str().to_owned();
            if metadata.jurisdictions.get(&country).is_none() {
                metadata
                    .jurisdictions
                    .insert(country.clone(), HashSet::new());
            }
            let mut handle = ExprHandle::new(format!("country({})", country.as_str()), true);
            handle.country = Some(country);
            Ok(handle)
        }
        Rule::province_expr => {
            let jur_pair = pair
                .into_inner()
                .next()
                .ok_or_else(|| format_err!("province_expr is empty"))?;
            let province = jur_pair.as_span().as_str().to_owned();
            let mut handle = ExprHandle::new(format!("province(\"{}\")", province.as_str()), true);
            handle.province.insert(province);
            Ok(handle)
        }
        a => Err(format_err!(
            "expected country_expr or province_expr got {:?}",
            a
        )),
    }
}

fn compile_interest_expr(
    pairs: &mut Pairs<'_, Rule>,
    _metadata: &mut PossibleValues,
) -> Result<ExprHandle, Error> {
    let pairs: Vec<_> = pairs.collect();
    let interest = match pairs
        .get(0)
        .ok_or_else(|| format_err!("Could not find interest type: {:?}", pairs))?
        .as_span()
        .as_str()
    {
        "pni" => Interest::PNI,
        "io" => Interest::IO,
        a => bail!("Could not find the interest type from {}", a),
    };

    let handle = format!("interest(\"{}\")", interest);
    Ok(ExprHandle {
        interest: Some(interest),
        ..ExprHandle::new(handle, true)
    })
}

fn compile_not_expr(
    ctx: &mut CompileContext,
    outfile: &mut File,
    pairs: Pairs<'_, Rule>,
    metadata: &mut PossibleValues,
) -> Result<ExprHandle, Error> {
    let expr_handle = compile_expr(ctx, outfile, pairs, metadata, false)?;
    if expr_handle.atomic {
        let mut handle = ExprHandle::new(format!("not {}", expr_handle), false);
        handle.province = expr_handle.province;
        Ok(handle)
    } else {
        let atomic_handle = ctx.get_expr_handle();
        if ctx.emit() {
            outfile.write_all(format!("{} :- {}.\n", atomic_handle, expr_handle).as_bytes())?;
        }
        let mut handle = ExprHandle::new(format!("not {}", atomic_handle), false);
        handle.province = expr_handle.province;
        Ok(handle)
    }
}

fn compile_is_personal() -> Result<ExprHandle, Error> {
    let mut handle = ExprHandle::new("personal".to_string(), true);
    handle.contains_personal = true;
    Ok(handle)
}

fn compile_is_business() -> Result<ExprHandle, Error> {
    let mut handle = ExprHandle::new("business".to_string(), true);
    handle.contains_business = true;
    Ok(handle)
}

fn compile_true_val(ctx: &mut CompileContext, outfile: &mut File) -> Result<ExprHandle, Error> {
    let handle = ctx.get_expr_handle();
    if ctx.emit() {
        outfile.write_all(format!("{}.\n", handle).as_bytes())?;
    }
    Ok(handle)
}

fn compile_expr(
    ctx: &mut CompileContext,
    outfile: &mut File,
    pairs: Pairs<'_, Rule>,
    metadata: &mut PossibleValues,
    is_expr_from_fee_stmt: bool,
) -> Result<ExprHandle, Error> {
    let mut ret: Option<ExprHandle> = None;
    let mut op: Option<BoolOp> = None;
    for pair in pairs {
        match pair.as_rule() {
            Rule::bool => {
                let bool_inner = pair
                    .into_inner()
                    .next()
                    .ok_or_else(|| format_err!("bool does not contain a val or expression"))?;
                let new_ret = match bool_inner.as_rule() {
                    Rule::expr => compile_expr(
                        ctx,
                        outfile,
                        bool_inner.into_inner(),
                        metadata,
                        is_expr_from_fee_stmt,
                    )?,
                    Rule::percentage_comp_expr => {
                        compile_percentage_comp_expr(ctx, bool_inner.into_inner(), metadata)?
                    }
                    Rule::amount_comp_expr => compile_amount_comp_expr(
                        ctx,
                        bool_inner.into_inner(),
                        metadata,
                        is_expr_from_fee_stmt,
                    )?,
                    Rule::fee_comp_expr => {
                        compile_fee_comp_expr(ctx, bool_inner.into_inner(), metadata)?
                    }
                    Rule::time_comp_expr => {
                        compile_time_comp_expr(ctx, bool_inner.into_inner(), metadata)?
                    }
                    Rule::amount_to_customer_comp_expr => {
                        compile_amount_to_customer_comp_expr(ctx, bool_inner.into_inner(), metadata)?
                    }
                    Rule::jur_expr => compile_jur_expr(&mut bool_inner.into_inner(), metadata)?,
                    Rule::interest_expr => {
                        compile_interest_expr(&mut bool_inner.into_inner(), metadata)?
                    }
                    Rule::not_expr => {
                        compile_not_expr(ctx, outfile, bool_inner.into_inner(), metadata)?
                    }
                    Rule::is_personal => compile_is_personal()?,
                    Rule::is_business => compile_is_business()?,
                    Rule::true_val => compile_true_val(ctx, outfile)?,
                    Rule::false_val => ctx.get_expr_handle(),
                    a => return Err(format_err!("expected val or expression, found {:?}", a)),
                };
                match (ret, op) {
                    (None, None) => ret = Some(new_ret),
                    (Some(old_ret), Some(BoolOp::And)) => {
                        let mut handle =
                            ExprHandle::new(format!("{}, {}", old_ret, new_ret), false);
                        handle.country = old_ret.country.or(new_ret.country);
                        handle.province = old_ret
                            .province
                            .into_iter()
                            .chain(new_ret.province)
                            .collect();
                        handle.interest = valid_option_and(old_ret.interest, new_ret.interest)?;

                        handle.contains_business =
                            old_ret.contains_business || new_ret.contains_business;
                        handle.contains_personal =
                            old_ret.contains_personal || new_ret.contains_personal;
                        if handle.contains_business && handle.contains_personal {
                            return Err(format_err!("expression contains a contradiction: no loan can be both business and personal"));
                        }
                        op = None;
                        ret = Some(handle);
                    }
                    (Some(old_ret), Some(BoolOp::Or)) => {
                        let mut handle = ctx.get_expr_handle();
                        if ctx.emit() {
                            outfile.write_all(
                                format!("{} :- {}.\n{} :- {}.\n", handle, old_ret, handle, new_ret)
                                    .as_bytes(),
                            )?;
                        }
                        handle.country = old_ret.country.and(new_ret.country);
                        handle.province = old_ret
                            .province
                            .into_iter()
                            .chain(new_ret.province)
                            .collect();

                        handle.interest = valid_option_and(old_ret.interest, new_ret.interest)?;
                        handle.contains_business =
                            old_ret.contains_business && new_ret.contains_business;
                        handle.contains_personal =
                            old_ret.contains_personal && new_ret.contains_personal;
                        if handle.contains_personal && handle.contains_business {
                            handle.contains_business = false;
                            handle.contains_personal = false;
                        }
                        op = None;
                        ret = Some(handle);
                    }
                    _ => {
                        return Err(format_err!(
                            "expression must be of the format `bool (bool_op bool)*`"
                        ));
                    }
                }
            }
            Rule::bool_op => {
                op = match pair
                    .into_inner()
                    .next()
                    .ok_or_else(|| format_err!("bool_op must contain an operator"))?
                    .as_rule()
                {
                    Rule::and_op => Some(BoolOp::And),
                    Rule::or_op => Some(BoolOp::Or),
                    a => return Err(format_err!("expected and_op or or_op, found {:?}", a)),
                }
            }
            a => {
                if ret.is_some() ^ op.is_some() {
                    return Err(format_err!("expect bool_op found {:?}", a));
                } else {
                    return Err(format_err!("expect bool found {:?}", a));
                }
            }
        }
    }
    ret.ok_or_else(|| format_err!("expr did not produce a handle"))
}

fn compile_for_stmt(
    ctx: &mut CompileContext,
    outfile: &mut File,
    pairs: Pairs<'_, Rule>,
    metadata: &mut PossibleValues,
) -> Result<(), Error> {
    let pairs_vec: Vec<Pair<'_, Rule>> = pairs.collect();
    let new_handle = compile_expr(
        ctx,
        outfile,
        pairs_vec
            .get(0)
            .ok_or_else(|| format_err!("FOR statement does not contain an expression"))?
            .clone()
            .into_inner(),
        metadata,
        false,
    )?;
    ctx.push_scope(new_handle);
    compile_newline(
        ctx,
        pairs_vec
            .get(1)
            .ok_or_else(|| format_err!("FOR statement does not contain an expression"))?
            .clone(),
    )?;
    Ok(())
}

fn determine_fee_cap_from_expression(expression: &str) -> Result<Option<f64>, Error> {
    // Analyze the UNFEEABLE expression to determine the appropriate fee cap
    // Based on the requirements:
    // - UNFEEABLE WHEN fee > 0 AND amountToCustomer > 30k => cap at 0
    // - UNFEEABLE WHEN fee > 500 => cap at 500
    // - UNFEEABLE WHEN fee > 100 AND amountToCustomer > 30k => cap at 100
    // - UNFEEABLE WHEN fee > 0 AND amount > 15k => cap at 0
    // - UNFEEABLE WHEN fee > 0 AND apr > 10% => cap at 0
    // - UNFEEABLE WHEN fee > 100 AND apr > 10% => cap at 100
    // - UNFEEABLE WHEN fee > 0 => cap at 0

    // Check for specific patterns in the expression (using scaled values)
    // fee > 500 scales to FEE > 10 (500 * fee_scale / 1000 = 500 * 20 / 1000 = 10)
    // fee > 300 scales to FEE > 6 (300 * fee_scale / 1000 = 300 * 20 / 1000 = 6)
    // fee > 100 scales to FEE > 2 (100 * fee_scale / 1000 = 100 * 20 / 1000 = 2)
    // fee > 0 scales to FEE > 0

    if expression.contains("FEE > 10") {
        Ok(Some(500.0))
    } else if expression.contains("FEE > 6") {
        Ok(Some(300.0))
    } else if expression.contains("FEE > 2") &&
              (expression.contains("amountToCustomer") || expression.contains("APR")) {
        Ok(Some(100.0))
    } else if expression.contains("FEE > 0") {
        Ok(Some(0.0))
    } else {
        // Not a fee capping rule, use regular unfeeable behavior
        Ok(None)
    }
}

fn compile_feeable_stmt(
    feeable: bool,
    ctx: &mut CompileContext,
    outfile: &mut File,
    pairs: &mut Pairs<'_, Rule>,
    metadata: &mut PossibleValues,
) -> Result<(), Error> {
    let pairs_vec: Vec<Pair<'_, Rule>> = pairs.collect();
    let mut expr_handle = {
        if pairs_vec.len() > 1 {
            // safely unwrap because there is an expr here if the count is more than 1
            compile_expr(
                ctx,
                outfile,
                pairs_vec.get(0).unwrap().clone().into_inner(),
                metadata,
                true,
            )?
        } else {
            ctx.get_expr_handle()
        }
    };

    if !ctx.scope.is_empty() {
        let scope_exprs = ctx
            .scope
            .clone()
            .into_iter()
            .map(|h| {
                expr_handle.province = [expr_handle.province.clone(), h.province.clone()]
                    .iter()
                    .flat_map(|a| a.clone())
                    .collect();
                expr_handle.country = expr_handle.country.clone().or_else(|| h.country.clone());
                expr_handle.contains_business =
                    expr_handle.contains_business || h.contains_business;
                expr_handle.contains_personal =
                    expr_handle.contains_personal || h.contains_personal;
                format!("{}", h)
            })
            .collect::<Vec<String>>()
            .join(", ");
        if ctx.emit() {
            if !feeable {
                // For UNFEEABLE rules, check if this is a fee capping rule
                let fee_cap = determine_fee_cap_from_expression(&expr_handle.handle)?;
                if let Some(cap_value) = fee_cap {
                    // Generate fee capping constraint instead of unfeeable rule
                    let fee_cap_scaled = (cap_value * (metadata.fee_scale as f64 / 1000.0)) as i32;

                    // Remove fee conditions from the original expression since we're replacing with fee cap
                    let mut modified_expr = expr_handle.handle
                        .replace("fee(FEE), FEE > 0, ", "")
                        .replace("fee(FEE), FEE > 2, ", "")
                        .replace("fee(FEE), FEE > 6, ", "")
                        .replace("fee(FEE), FEE > 10, ", "")
                        .replace(", fee(FEE), FEE > 0", "")
                        .replace(", fee(FEE), FEE > 2", "")
                        .replace(", fee(FEE), FEE > 6", "")
                        .replace(", fee(FEE), FEE > 10", "");

                    // Handle case where fee condition is the entire expression
                    if modified_expr == "fee(FEE), FEE > 0" ||
                       modified_expr == "fee(FEE), FEE > 2" ||
                       modified_expr == "fee(FEE), FEE > 6" ||
                       modified_expr == "fee(FEE), FEE > 10" {
                        modified_expr = "".to_string();
                    }

                    if pairs_vec.len() == 1 || modified_expr.is_empty() {
                        outfile.write_all(
                            format!(":- fee(A), feerange(A), A > {}, {}.\n", fee_cap_scaled, scope_exprs)
                            .as_bytes()
                        )?;
                    } else {
                        outfile.write_all(
                            format!(":- fee(A), feerange(A), A > {}, {}, {}.\n", fee_cap_scaled, modified_expr, scope_exprs)
                            .as_bytes()
                        )?;
                    }
                } else {
                    // Regular unfeeable rule
                    let feeability = "unfeeable";
                    if pairs_vec.len() == 1 {
                        outfile.write_all(format!("{} :- {}.\n", feeability, scope_exprs).as_bytes())?;
                    } else {
                        outfile.write_all(
                            format!("{} :- {}, {}.\n", feeability, expr_handle, scope_exprs).as_bytes(),
                        )?;
                    }
                }
            } else {
                // Regular feeable rule
                let feeability = "feeable";
                if pairs_vec.len() == 1 {
                    outfile.write_all(format!("{} :- {}.\n", feeability, scope_exprs).as_bytes())?;
                } else {
                    outfile.write_all(
                        format!("{} :- {}, {}.\n", feeability, expr_handle, scope_exprs).as_bytes(),
                    )?;
                }
            }
        }
    } else {
        return Err(format_err!("FEEABLE must be scoped within a FOR block"));
    }

    if !expr_handle.province.is_empty() && expr_handle.country.is_none() {
        return Err(format_err!(
            "expr cannot contain a province without a country"
        ));
    } else if let Some(country) = expr_handle.country {
        if let Some(provinces) = metadata.jurisdictions.get_mut(&country) {
            provinces.extend(expr_handle.province);
        } else {
            unreachable!();
        }
    }

    compile_newline(
        ctx,
        pairs_vec
            .last()
            .ok_or_else(|| format_err!("feeable_stmt must be terminated by a newline"))?
            .clone(),
    )?;
    Ok(())
}

fn compile_lendable_stmt(
    ctx: &mut CompileContext,
    outfile: &mut File,
    pairs: &mut Pairs<'_, Rule>,
    metadata: &mut PossibleValues,
) -> Result<(), Error> {
    let mut expr_handle = ctx.get_expr_handle();
    if !ctx.scope.is_empty() {
        let scope_exprs = ctx
            .scope
            .clone()
            .into_iter()
            .map(|h| {
                expr_handle.province = [expr_handle.province.clone(), h.province.clone()]
                    .iter()
                    .flat_map(|a| a.clone())
                    .collect();
                expr_handle.country = expr_handle.country.clone().or_else(|| h.country.clone());
                expr_handle.contains_business =
                    expr_handle.contains_business || h.contains_business;
                expr_handle.contains_personal =
                    expr_handle.contains_personal || h.contains_personal;
                format!("{}", h)
            })
            .collect::<Vec<String>>()
            .join(", ");
        if ctx.emit() {
            outfile.write_all(format!("lendable :- {}.\n", scope_exprs).as_bytes())?;
        }
    } else {
        return Err(format_err!("LENDABLE must be scoped within a FOR block"));
    }
    if !expr_handle.province.is_empty() && expr_handle.country.is_none() {
        return Err(format_err!(
            "expr cannot contain a province without a country"
        ));
    } else if let Some(country) = expr_handle.country {
        if let Some(provinces) = metadata.jurisdictions.get_mut(&country) {
            provinces.extend(expr_handle.province);
        } else {
            unreachable!();
        }
    }
    compile_newline(
        ctx,
        pairs
            .next()
            .ok_or_else(|| format_err!("lendable_stmt must be terminated by a newline"))?,
    )?;
    Ok(())
}

fn compile_if_stmt(
    predicate: &str,
    ctx: &mut CompileContext,
    outfile: &mut File,
    pairs: Pairs<'_, Rule>,
    metadata: &mut PossibleValues,
) -> Result<(), Error> {
    let pairs_vec: Vec<Pair<'_, Rule>> = pairs.collect();
    let mut expr_handle = compile_expr(
        ctx,
        outfile,
        pairs_vec
            .get(0)
            .ok_or_else(|| format_err!("{}if_stmt must contain an expr", predicate))?
            .clone()
            .into_inner(),
        metadata,
        false,
    )?;
    if !ctx.scope.is_empty() {
        let scope_exprs = ctx
            .scope
            .clone()
            .into_iter()
            .map(|h| {
                expr_handle.province = [expr_handle.province.clone(), h.province.clone()]
                    .iter()
                    .flat_map(|a| a.clone())
                    .collect();
                expr_handle.country = expr_handle.country.clone().or_else(|| h.country.clone());
                expr_handle.contains_business =
                    expr_handle.contains_business || h.contains_business;
                expr_handle.contains_personal =
                    expr_handle.contains_personal || h.contains_personal;
                format!("{}", h)
            })
            .collect::<Vec<String>>()
            .join(", ");
        if ctx.emit() {
            outfile.write_all(
                format!("{} :- {}, {}.\n", predicate, expr_handle, scope_exprs).as_bytes(),
            )?;
        }
    } else if ctx.emit() {
        outfile.write_all(format!("{} :- {}.\n", predicate, expr_handle).as_bytes())?;
    }

    if !expr_handle.province.is_empty() && expr_handle.country.is_none() {
        return Err(format_err!(
            "expr cannot contain a province without a country"
        ));
    } else if let Some(country) = expr_handle.country {
        if let Some(provinces) = metadata.jurisdictions.get_mut(&country) {
            provinces.extend(expr_handle.province);
        } else {
            unreachable!();
        }
    }
    if expr_handle.contains_business && expr_handle.contains_personal {
        return Err(format_err!(
            "expression contains a contradiction: no loan can be both business and personal"
        ));
    };
    compile_newline(
        ctx,
        pairs_vec
            .get(1)
            .ok_or_else(|| format_err!("{}if_stmt must be terminated by a newline", predicate))?
            .clone(),
    )?;
    Ok(())
}

fn compile(
    ctx: &mut CompileContext,
    outfile: &mut File,
    pairs: Pairs<'_, Rule>,
    metadata: &mut PossibleValues,
) -> Result<(), Error> {
    for pair in pairs {
        match pair.as_rule() {
            Rule::amount_range => compile_amount_range(ctx, outfile, pair.into_inner(), metadata)?,
            Rule::term_range => compile_term_range(ctx, outfile, pair.into_inner(), metadata)?,
            Rule::for_stmt => compile_for_stmt(ctx, outfile, pair.into_inner(), metadata)?,
            Rule::unfeeable_stmt => {
                compile_feeable_stmt(false, ctx, outfile, &mut pair.into_inner(), metadata)?
            }
            Rule::feeable_stmt => {
                compile_feeable_stmt(true, ctx, outfile, &mut pair.into_inner(), metadata)?
            }
            Rule::lendable_stmt => {
                compile_lendable_stmt(ctx, outfile, &mut pair.into_inner(), metadata)?
            }
            Rule::illegalif_stmt => {
                compile_if_stmt("illegal", ctx, outfile, pair.into_inner(), metadata)?
            }
            Rule::legalif_stmt => {
                compile_if_stmt("legal", ctx, outfile, pair.into_inner(), metadata)?
            }
            Rule::newline => compile_newline(ctx, pair)?,
            _ => (),
        }
    }
    for (country, provinces) in &metadata.jurisdictions {
        if ctx.emit() {
            outfile.write_all(format!("#external country({}).\n", country).as_bytes())?;
        }
        for province in provinces.iter() {
            if ctx.emit() {
                outfile.write_all(format!("#external province(\"{}\").\n", province).as_bytes())?;
            }
        }
    }
    outfile.write_all(b"#external interest(\"PNI\").\n")?;
    outfile.write_all(b"#external interest(\"IO\").\n")?;
    metadata.amount_scale = metadata.amounts.iter().fold(1, |acc, val| {
        use num::integer::lcm;
        lcm(acc, *val.denom().unwrap())
    }) * 2;
    let mut amounts: Vec<i32> = metadata
        .amounts
        .iter()
        .map(|amt| *amt.numer().unwrap() * (metadata.amount_scale / *amt.denom().unwrap()))
        .collect();
    amounts.sort();
    if ctx.emit() {
        for amount in &amounts {
            outfile.write_all(format!("amountrange({}).\n", amount).as_bytes())?;
        }
        for win in amounts.windows(2) {
            outfile.write_all(format!("amountrange({}).\n", win[1] - 1).as_bytes())?;
        }
        outfile.write_all(
            format!(
                ":- amount(A), amountrange(A), A < {}.\n",
                metadata.min_amount.unwrap().numer().unwrap()
                    * (metadata.amount_scale / metadata.min_amount.unwrap().denom().unwrap())
            )
            .as_bytes(),
        )?;
        outfile.write_all(
            format!(
                ":- amount(A), amountrange(A), A > {}.\n",
                metadata.max_amount.unwrap().numer().unwrap()
                    * (metadata.amount_scale / metadata.max_amount.unwrap().denom().unwrap())
            )
            .as_bytes(),
        )?;
    }

    metadata.amt_scale = metadata.amts.iter().fold(1, |acc, val| {
        use num::integer::lcm;
        lcm(acc, *val.denom().unwrap())
    }) * 2;
    let mut amts: Vec<i32> = metadata
        .amts
        .iter()
        .map(|lamt| *lamt.numer().unwrap() * (metadata.amt_scale / *lamt.denom().unwrap()))
        .collect();
    amts.sort();
    if ctx.emit() {
        for amt in &amts {
            outfile.write_all(format!("amtrange({}).\n", amt).as_bytes())?;
        }
        for win in amts.windows(2) {
            outfile.write_all(format!("amtrange({}).\n", win[1] - 1).as_bytes())?;
        }
        outfile.write_all(
            format!(
                ":- amt(A), amtrange(A), A < {}.\n",
                metadata.min_amt.unwrap().numer().unwrap()
                    * (metadata.amt_scale / metadata.min_amt.unwrap().denom().unwrap())
            )
            .as_bytes(),
        )?;
        outfile.write_all(
            format!(
                ":- amt(A), amtrange(A), A > {}.\n",
                metadata.max_amt.unwrap().numer().unwrap()
                    * (metadata.amt_scale / metadata.max_amt.unwrap().denom().unwrap())
            )
            .as_bytes(),
        )?;
    }

    metadata.term_scale = metadata.terms.iter().fold(1, |acc, val| {
        use num::integer::lcm;
        lcm(acc, *val.denom().unwrap())
    }) * 2;
    let mut terms: Vec<i32> = metadata
        .terms
        .iter()
        .map(|term| *term.numer().unwrap() * (metadata.term_scale / *term.denom().unwrap()))
        .collect();
    terms.sort();
    if ctx.emit() {
        for term in &terms {
            outfile.write_all(format!("termrange({}).\n", term).as_bytes())?;
        }
        for win in terms.windows(2) {
            outfile.write_all(format!("termrange({}).\n", win[1] - 1).as_bytes())?;
        }
        outfile.write_all(
            format!(
                ":- term(A), termrange(A), A < {}.\n",
                metadata.min_term.unwrap().numer().unwrap()
                    * (metadata.term_scale / metadata.min_term.unwrap().denom().unwrap())
            )
            .as_bytes(),
        )?;
        outfile.write_all(
            format!(
                ":- term(A), termrange(A), A > {}.\n",
                metadata.max_term.unwrap().numer().unwrap()
                    * (metadata.term_scale / metadata.max_term.unwrap().denom().unwrap())
            )
            .as_bytes(),
        )?;
    }

    metadata.apr_scale = metadata.aprs.iter().fold(1, |acc, val| {
        use num::integer::lcm;
        lcm(acc, *val.denom().unwrap())
    }) * 2;
    let mut aprs: Vec<i32> = metadata
        .aprs
        .iter()
        .map(|apr| *apr.numer().unwrap() * (metadata.apr_scale / *apr.denom().unwrap()))
        .collect();
    aprs.sort();
    if ctx.emit() {
        for apr in &aprs {
            outfile.write_all(format!("aprrange({}).\n", apr).as_bytes())?;
        }
        for win in aprs.windows(2) {
            outfile.write_all(format!("aprrange({}).\n", win[1] - 1).as_bytes())?;
        }
        outfile.write_all(
            format!(
                ":- apr(A), aprrange(A), A < {}.\n",
                Fraction::from(0.0).numer().unwrap()
                    * (metadata.apr_scale / Fraction::from(0.0).denom().unwrap())
            )
            .as_bytes(),
        )?;
        outfile.write_all(
            format!(
                ":- apr(A), aprrange(A), A > {}.\n",
                Fraction::from(100.0).numer().unwrap()
                    * (metadata.apr_scale / Fraction::from(100.0).denom().unwrap())
            )
            .as_bytes(),
        )?;
    }

    metadata.fee_scale = metadata.fees.iter().fold(1, |acc, val| {
        use num::integer::lcm;
        lcm(acc, *val.denom().unwrap())
    }) * 2;
    let mut fees: Vec<i32> = metadata
        .fees
        .iter()
        .map(|fee| *fee.numer().unwrap() * (metadata.fee_scale / *fee.denom().unwrap()))
        .collect();
    fees.sort();
    if ctx.emit() {
        for fee in &fees {
            outfile.write_all(format!("feerange({}).\n", fee).as_bytes())?;
        }
        for win in fees.windows(2) {
            outfile.write_all(format!("feerange({}).\n", win[1] - 1).as_bytes())?;
        }
        // minimum fee can default to 0 because a loan can possibly have no fees
        outfile.write_all(
            format!(
                ":- fee(A), feerange(A), A < {}.\n",
                Fraction::from(0.0).numer().unwrap()
                    * (metadata.fee_scale / Fraction::from(0.0).denom().unwrap())
            )
            .as_bytes(),
        )?;
        // amount is used for max fee since a loan's fee can be as much as the loan amount
        outfile.write_all(
            format!(
                ":- fee(A), feerange(A), A > {}.\n",
                (metadata.max_amt.unwrap().numer().unwrap() / 1000)
                    * (metadata.fee_scale / metadata.max_amt.unwrap().denom().unwrap())
            )
            .as_bytes(),
        )?;
    }

    // Generate amount_to_customer range facts
    // If no amountToCustomer references exist, use a minimal range to avoid overwhelming the solver
    if metadata.amount_to_customers.len() == 1 && metadata.amount_to_customers.contains(&Fraction::from(0.0)) {
        // No amountToCustomer references found in rules - use minimal range as default
        metadata.amount_to_customers.clear();
        metadata.amount_to_customers.insert(Fraction::from(0.0));
        metadata.amount_to_customers.insert(metadata.max_amount.unwrap_or(Fraction::from(1000000.0)));
        metadata.min_amount_to_customer = Some(Fraction::from(0.0));
        metadata.max_amount_to_customer = metadata.max_amount;
    } else {
        // Set proper min/max bounds based on actual values found
        let mut min_val = None;
        let mut max_val = None;
        for val in &metadata.amount_to_customers {
            match &min_val {
                None => min_val = Some(val.clone()),
                Some(current_min) => if val < current_min { min_val = Some(val.clone()); }
            }
            match &max_val {
                None => max_val = Some(val.clone()),
                Some(current_max) => if val > current_max { max_val = Some(val.clone()); }
            }
        }
        metadata.min_amount_to_customer = min_val;
        metadata.max_amount_to_customer = max_val;
    }

    metadata.amount_to_customer_scale = metadata.amount_to_customers.iter().fold(1, |acc, val| {
        use num::integer::lcm;
        lcm(acc, *val.denom().unwrap())
    }) * 2;
    let mut amount_to_customers: Vec<i32> = metadata
        .amount_to_customers
        .iter()
        .map(|amt| *amt.numer().unwrap() * (metadata.amount_to_customer_scale / *amt.denom().unwrap()))
        .collect();
    amount_to_customers.sort();
    if ctx.emit() {
        for amount_to_customer in &amount_to_customers {
            outfile.write_all(format!("amount_to_customer_range({}).\n", amount_to_customer).as_bytes())?;
        }
        for win in amount_to_customers.windows(2) {
            outfile.write_all(format!("amount_to_customer_range({}).\n", win[1] - 1).as_bytes())?;
        }
        // Use the min/max from metadata, or default to 0 and max amount
        let min_amount_to_customer = metadata.min_amount_to_customer.unwrap_or(Fraction::from(0.0));
        let max_amount_to_customer = metadata.max_amount_to_customer.unwrap_or(metadata.max_amount.unwrap_or(Fraction::from(1000000.0)));
        
        outfile.write_all(
            format!(
                ":- amount_to_customer(A), amount_to_customer_range(A), A < {}.\n",
                min_amount_to_customer.numer().unwrap()
                    * (metadata.amount_to_customer_scale / min_amount_to_customer.denom().unwrap())
            )
            .as_bytes(),
        )?;
        outfile.write_all(
            format!(
                ":- amount_to_customer(A), amount_to_customer_range(A), A > {}.\n",
                max_amount_to_customer.numer().unwrap()
                    * (metadata.amount_to_customer_scale / max_amount_to_customer.denom().unwrap())
            )
            .as_bytes(),
        )?;
    }

    Ok(())
}

fn main_err() -> Result<(), Error> {
    let args: Vec<String> = std::env::args().collect();
    let infile_path = &args
        .get(1)
        .ok_or_else(|| format_err!("usage: lendablec path/to/rules.lnd"))?;
    let code = std::fs::read_to_string(infile_path)?;
    let pairs =
        LendableParser::parse(Rule::tok_list, code.as_str()).unwrap_or_else(|e| panic!("{}", e));
    let mut outfile = File::create(".rules.lp.swp")?;
    let mut jur_file = File::create("metadata.cbor")?;
    let mut ctx1 = CompileContext::new(false);
    let mut ctx2 = CompileContext::new(true);
    let mut metadata = PossibleValues::new();

    compile(&mut ctx1, &mut outfile, pairs.clone(), &mut metadata).map_err(|e| {
        println!("Error at line #{} of {}:", ctx1.line, infile_path);
        e
    })?;
    compile(&mut ctx2, &mut outfile, pairs, &mut metadata).map_err(|e| {
        println!("Error at line #{} of {}:", ctx2.line, infile_path);
        e
    })?;
    serde_cbor::to_writer(&mut jur_file, &metadata)?;

    std::fs::rename(".rules.lp.swp", "../formulation/rules.lp")?;
    std::fs::rename("metadata.cbor", "../formulation/metadata.cbor")?;
    Ok(())
}

fn main() -> Result<(), ()> {
    match main_err() {
        Err(e) => {
            println!("{:?}", e);
            Err(())
        }
        Ok(_) => Ok(()),
    }
}
